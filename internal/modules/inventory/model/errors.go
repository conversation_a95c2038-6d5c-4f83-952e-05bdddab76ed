package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

// Inventory not found errors
func InventoryNotFoundf(message string, err error, data interface{}) error {
	return utils.NotFoundf(message, err, data)
}

// Inventory conflict errors
func InventoryConflictBatchCodef(message string, err error, data interface{}) error {
	return utils.ConflictErrorf(message, err, data)
}

// Inventory validation errors
func InventoryValidationErrorf(message string, err error, data interface{}) error {
	return utils.BadRequestf(message, err, data)
}

// Inventory business logic errors
func InventoryInsufficientStockf(message string, err error, data interface{}) error {
	return utils.BadRequestf(message, err, data)
}

func InventoryExpiredBatchf(message string, err error, data interface{}) error {
	return utils.BadRequestf(message, err, data)
}

func InventoryNegativeStockf(message string, err error, data interface{}) error {
	return utils.BadRequestf(message, err, data)
}
